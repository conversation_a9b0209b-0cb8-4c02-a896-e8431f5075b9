{"name": "tllv2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postbuild": "next-sitemap", "checklink": "pnpm exec tsc --noEmit --pretty"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@sanity/image-url": "^1.1.0", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.476.0", "motion": "^12.4.7", "next": "15.1.7", "next-intl": "^3.26.5", "next-sanity": "^9.12.0", "next-sitemap": "^4.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "typed.js": "^2.1.0", "types": "link:@sanity/image-url/lib/types/types"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}