import { Metadata } from "next";

export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  ogImage?: string;
  canonical?: string;
  noindex?: boolean;
}

export const siteConfig = {
  name: "TLL",
  url: "https://tll.jp",
  ogImage: "/assets/og-image.jpg",
  description: {
    ja: "TLLはAI特化の不動産投資会社として、データドリブンな意思決定で日本の不動産投資市場を革新します。Urbalyticsプラットフォームで透明性の高い投資をサポート。",
    en: "TLL is an AI-specialized real estate investment company revolutionizing Japan's property market through data-driven decisions and our proprietary Urbalytics platform.",
    zh: "TLL是一家AI特化的房地产投资公司，通过数据驱动决策和自主开发的Urbalytics平台革新日本房地产投资市场。",
  },
  keywords: {
    ja: "AI不動産投資, AI特化会社, データドリブン投資, Urbalytics, 不動産テック, PropTech, 東京不動産, 機械学習, 投資分析, TLL",
    en: "AI real estate investment, AI-specialized company, data-driven investment, Urbalytics, PropTech, real estate technology, Tokyo property, machine learning, investment analysis, TLL",
    zh: "AI房地产投资, AI特化公司, 数据驱动投资, Urbalytics, 房地产科技, PropTech, 东京房地产, 机器学习, 投资分析, TLL",
  },
};

export function generateMetadata({
  title,
  description,
  keywords,
  ogImage,
  canonical,
  noindex = false,
}: SEOData): Metadata {
  const fullTitle = title.includes("TLL") ? title : `${title} | TLL`;
  const ogImageUrl = ogImage || siteConfig.ogImage;
  const canonicalUrl = canonical ? `${siteConfig.url}${canonical}` : undefined;

  return {
    title: fullTitle,
    description,
    keywords,
    robots: noindex ? "noindex,nofollow" : "index,follow",
    // canonical: canonicalUrl,
    openGraph: {
      title: fullTitle,
      description,
      url: canonicalUrl,
      siteName: siteConfig.name,
      images: [
        {
          url: ogImageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: "ja_JP",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: fullTitle,
      description,
      images: [ogImageUrl],
    },
    alternates: {
      canonical: canonicalUrl,
      languages: {
        ja: canonicalUrl?.replace("/en/", "/ja/").replace("/zh/", "/ja/"),
        en: canonicalUrl?.replace("/ja/", "/en/").replace("/zh/", "/en/"),
        zh: canonicalUrl?.replace("/ja/", "/zh/").replace("/en/", "/zh/"),
      },
    },
  };
}

// Page-specific metadata generators
export const pageMetadata = {
  home: (locale: string) => ({
    title:
      locale === "en"
        ? "TLL - AI-Specialized Real Estate Investment Company | Urbalytics Platform"
        : locale === "zh"
          ? "TLL - AI特化房地产投资公司 | Urbalytics平台"
          : "TLL - AI特化不動産投資会社 | Urbalyticsプラットフォーム",
    description:
      siteConfig.description[locale as keyof typeof siteConfig.description],
    keywords: siteConfig.keywords[locale as keyof typeof siteConfig.keywords],
    canonical: `/${locale}`,
  }),

  about: (locale: string) => ({
    title:
      locale === "en"
        ? "About TLL - AI-Specialized Real Estate Investment Company"
        : locale === "zh"
          ? "关于TLL - AI特化房地产投资公司"
          : "TLL会社案内 - AI特化不動産投資会社",
    description:
      locale === "en"
        ? "TLL is an AI-specialized company transforming Japan's real estate investment market through machine learning, data analytics, and our proprietary Urbalytics platform for transparent, data-driven investment decisions."
        : locale === "zh"
          ? "TLL是一家AI特化公司，通过机器学习、数据分析和自主开发的Urbalytics平台，以透明的数据驱动投资决策改变日本房地产投资市场。"
          : "TLLはAI特化会社として、機械学習、データ分析、自社開発のUrbalyticsプラットフォームを通じて、透明性の高いデータドリブンな投資判断で日本の不動産投資市場を変革しています。",
    keywords:
      locale === "en"
        ? "AI real estate company, machine learning investment, PropTech innovation, Urbalytics platform, data analytics"
        : locale === "zh"
          ? "AI房地产公司, 机器学习投资, PropTech创新, Urbalytics平台, 数据分析"
          : "AI不動産会社, 機械学習投資, PropTech革新, Urbalyticsプラットフォーム, データ分析",
    canonical: `/${locale}/about`,
  }),

  team: (locale: string) => ({
    title:
      locale === "en"
        ? "Our Team - TLL"
        : locale === "zh"
          ? "我们的团队 - TLL"
          : "チーム - TLL",
    description:
      locale === "en"
        ? "Meet TLL's experienced team of real estate investment professionals from Tokyo, Singapore, Shanghai, and the US."
        : locale === "zh"
          ? "认识TLL经验丰富的房地产投资专业团队，来自东京、新加坡、上海和美国。"
          : "東京、シンガポール、上海、米国の経験豊富な不動産投資プロフェッショナルチームをご紹介します。",
    keywords:
      locale === "en"
        ? "TLL team, real estate professionals, investment experts, global team"
        : locale === "zh"
          ? "TLL团队, 房地产专业人士, 投资专家, 全球团队"
          : "TLL チーム, 不動産プロフェッショナル, 投資専門家, グローバルチーム",
    canonical: `/${locale}/team`,
  }),

  inquiry: (locale: string) => ({
    title:
      locale === "en"
        ? "Contact Us - TLL"
        : locale === "zh"
          ? "联系我们 - TLL"
          : "お問い合わせ - TLL",
    description:
      locale === "en"
        ? "Contact TLL for real estate investment consultations and inquiries. We're here to support your investment journey."
        : locale === "zh"
          ? "联系TLL进行房地产投资咨询和询问。我们在这里支持您的投资之旅。"
          : "不動産投資のご相談・お問い合わせはTLLまで。お客様の投資をサポートいたします。",
    keywords:
      locale === "en"
        ? "TLL contact, real estate consultation, investment inquiry, contact form"
        : locale === "zh"
          ? "TLL联系, 房地产咨询, 投资询问, 联系表单"
          : "TLL お問い合わせ, 不動産相談, 投資相談, 問い合わせフォーム",
    canonical: `/${locale}/inquiry`,
  }),

  teamMember: (locale: string, memberName: string, memberTitle: string) => ({
    title:
      locale === "en"
        ? `${memberName} - ${memberTitle} | TLL Team`
        : locale === "zh"
          ? `${memberName} - ${memberTitle} | TLL团队`
          : `${memberName} - ${memberTitle} | TLL チーム`,
    description:
      locale === "en"
        ? `Learn about ${memberName}, ${memberTitle} at TLL. Discover their background and expertise in real estate investment.`
        : locale === "zh"
          ? `了解${memberName}，TLL的${memberTitle}。了解他们在房地产投资方面的背景和专业知识。`
          : `TLLの${memberTitle}、${memberName}についてご紹介。不動産投資における経歴と専門知識をご覧ください。`,
    keywords:
      locale === "en"
        ? `${memberName}, TLL team member, real estate professional, ${memberTitle}`
        : locale === "zh"
          ? `${memberName}, TLL团队成员, 房地产专业人士, ${memberTitle}`
          : `${memberName}, TLL チームメンバー, 不動産プロフェッショナル, ${memberTitle}`,
    canonical: `/${locale}/team/${memberName.toLowerCase().replace(/\s+/g, "-")}`,
  }),

  agent: (locale: string) => ({
    title:
      locale === "en"
        ? "Join TLL Agent Program - High Commission Real Estate Partnership"
        : locale === "zh"
          ? "加入TLL代理计划 - 高佣金房地产合作伙伴"
          : "TLLエージェントプログラム - 高報酬不動産パートナーシップ",
    description:
      locale === "en"
        ? "Join TLL's exclusive agent program with 75% commission rates, full remote work flexibility, and comprehensive support. Transform your real estate career with our AI-powered platform and professional backing."
        : locale === "zh"
          ? "加入TLL独家代理计划，享受75%佣金率、完全远程工作灵活性和全面支持。通过我们的AI驱动平台和专业支持改变您的房地产职业生涯。"
          : "TLL独占エージェントプログラムに参加し、75%の手数料率、完全リモートワークの柔軟性、包括的なサポートを享受。AI搭載プラットフォームとプロフェッショナルなバックアップで不動産キャリアを変革。",
    keywords:
      locale === "en"
        ? "TLL agent program, real estate agent, high commission, remote work, real estate partnership, AI platform, property agent"
        : locale === "zh"
          ? "TLL代理计划, 房地产代理, 高佣金, 远程工作, 房地产合作伙伴, AI平台, 物业代理"
          : "TLL エージェントプログラム, 不動産エージェント, 高報酬, リモートワーク, 不動産パートナーシップ, AIプラットフォーム, 物件エージェント",
    canonical: `/${locale}/agent`,
  }),
};
