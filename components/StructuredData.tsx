interface StructuredDataProps {
  data: object;
}

export default function StructuredData({ data }: StructuredDataProps) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(data)
      }}
    />
  );
}

// Predefined structured data schemas
export const organizationSchema = (locale: string) => ({
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "TLL",
  "url": "https://tll.jp",
  "logo": "https://tll.jp/assets/logo.png",
  "description": locale === 'en' ? 
    "TLL supports your asset formation as a real estate investment professional." :
    locale === 'zh' ? 
    "TLL作为房地产投资专业人士，支持您的资产形成。" :
    "TLLは不動産投資のプロフェッショナルとして、お客様の資産形成をサポートします。",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "神泉町11-11 イーデンビル3F",
    "addressLocality": "渋谷区",
    "addressRegion": "東京都",
    "postalCode": "150-0045",
    "addressCountry": "JP"
  },
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "+81-3-4566-3208",
    "contactType": "customer service",
    "availableLanguage": ["Japanese", "English", "Chinese"]
  },
  "sameAs": [
    "https://www.facebook.com/urbalytics"
  ]
});

export const localBusinessSchema = (locale: string) => ({
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  "name": "TLL",
  "image": "https://tll.jp/assets/logo.png",
  "url": "https://tll.jp",
  "telephone": "+81-3-4566-3208",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "神泉町11-11 イーデンビル3F",
    "addressLocality": "渋谷区",
    "addressRegion": "東京都",
    "postalCode": "150-0045",
    "addressCountry": "JP"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": 35.6580339,
    "longitude": 139.6917064
  },
  "openingHoursSpecification": {
    "@type": "OpeningHoursSpecification",
    "dayOfWeek": [
      "Monday",
      "Tuesday", 
      "Thursday",
      "Friday",
      "Saturday"
    ],
    "opens": "09:30",
    "closes": "18:00"
  },
  "priceRange": "$$"
});

export const personSchema = (member: any, locale: string) => ({
  "@context": "https://schema.org",
  "@type": "Person",
  "name": member.name,
  "alternateName": member.nameEn,
  "jobTitle": member.title,
  "description": member.description,
  "worksFor": {
    "@type": "Organization",
    "name": "TLL"
  },
  "image": `https://tll.jp/assets${member.image}`,
  "url": `https://tll.jp/${locale}/team/${member.nameEn.toLowerCase().replace(/\s+/g, '-')}`
});

export const breadcrumbSchema = (items: Array<{name: string, url: string}>) => ({
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": items.map((item, index) => ({
    "@type": "ListItem",
    "position": index + 1,
    "name": item.name,
    "item": `https://tll.jp${item.url}`
  }))
});
