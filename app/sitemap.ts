import { MetadataRoute } from "next";


// import { getServerSideSitemap } from 'next-sitemap';
// import { fetchPosts } from '@/lib/api'; // 你的数据获取方法

// export async function GET() {
//   const posts = await fetchPosts(); // 获取所有博客文章
//   const fields = posts.map(post => ({
//     loc: `https://yourwebsite.com/blog/${post.slug}`,
//     lastmod: new Date(post.updatedAt).toISOString(),
//     changefreq: 'weekly',
//     priority: 0.8,
//   }));

//   return getServerSideSitemap(fields);
// }


export default function sitemap(): MetadataRoute.Sitemap {
  return [
    {
      url: "https://tll.jp/company",
      lastModified: new Date().toISOString(),
    },
    {
      url: "https://tll.jp/team",
      lastModified: new Date().toISOString(),
    },
    {
      url: "https://tll.jp/inquiry",
      lastModified: new Date().toISOString(),
    },
    {
      url: "https://tll.jp/team/chen-tan",
      lastModified: new Date().toISOString(),
    },
    {
      url: "https://tll.jp/team/catherine-lu",
      lastModified: new Date().toISOString(),
    },
    {
      url: "https://tll.jp/team/xingkai-luo",
      lastModified: new Date().toISOString(),
    },
    {
      url: "https://tll.jp/team/xiao-li",
      lastModified: new Date().toISOString(),
    },
  ];
}