import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Analytics } from "@vercel/analytics/react";

import { NextIntlClientProvider } from "next-intl";
import { getMessages } from "next-intl/server";
import Script from "next/script";
import { generateMetadata as generateSEOMetadata, pageMetadata } from '@/lib/metadata';
import Header from "./Header";
import Footer from "./Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const homePageData = pageMetadata.home(locale);

  return generateSEOMetadata(homePageData);
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  // Note: Locale validation is handled by middleware.ts
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <head>
        {/* Hreflang tags for multilingual SEO */}
        <link rel="alternate" hrefLang="ja" href={`https://tll.jp/ja`} />
        <link rel="alternate" hrefLang="en" href={`https://tll.jp/en`} />
        <link rel="alternate" hrefLang="zh" href={`https://tll.jp/zh`} />
        <link rel="alternate" hrefLang="x-default" href={`https://tll.jp/ja`} />

        {/* Additional SEO meta tags */}
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#000000" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

        {/* Structured Data for Organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "TLL",
              "url": "https://tll.jp",
              "logo": "https://tll.jp/assets/logo.png",
              "description": locale === 'en' ?
                "TLL supports your asset formation as a real estate investment professional." :
                locale === 'zh' ?
                "TLL作为房地产投资专业人士，支持您的资产形成。" :
                "TLLは不動産投資のプロフェッショナルとして、お客様の資産形成をサポートします。",
              "address": {
                "@type": "PostalAddress",
                "streetAddress": "神泉町11-11 イーデンビル3F",
                "addressLocality": "渋谷区",
                "addressRegion": "東京都",
                "postalCode": "150-0045",
                "addressCountry": "JP"
              },
              "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "+81-3-4566-3208",
                "contactType": "customer service",
                "availableLanguage": ["Japanese", "English", "Chinese"]
              },
              "sameAs": [
                "https://www.facebook.com/urbalytics"
              ]
            })
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <NextIntlClientProvider locale={locale} messages={messages}>
          <Header extraClass="bg-transparent text-white"
            isHomePage={true} />

          <main className="flex-grow">
            {children}
            <Footer />
          </main>
        </NextIntlClientProvider>
        <Analytics />

        <Script
          src={`//code.tidio.co/nrifumgr0mihac1f0ewr31qz2etshriv.js?language=${locale}`}
          strategy="afterInteractive"
        />
      </body>
    </html>
  );
}
