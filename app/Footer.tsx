"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Mail, Phone, Facebook, Instagram, Twitter, ArrowUp, Link2, LinkIcon, ArrowRight, Globe } from "lucide-react";
import { Link, useRouter } from "@/i18n/routing";
import { track } from "@vercel/analytics";
import { useParams } from "next/navigation";
import { usePathname } from "next/navigation";
import { routing } from "@/i18n/routing";
import { useTranslations } from "next-intl";


export default function Footer() {
  const t = useTranslations("Footer");
  const params = useParams();
  const currentLocale = params.locale as string;
  const pathname = usePathname();

  const localeLabels = {
    ja: "日本語",
    zh: "中文",
    en: "English"
  };

  const handleLanguageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newLocale = event.target.value;
    const newPath = pathname.replace(`/${currentLocale}`, `/${newLocale}`);
    window.location.href = newPath;
  };

  return (
    <footer className="bg-black text-white">
      {/* Contact Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 justify-between items-center md:items-start max-w-6xl mx-auto px-6 py-6 border-b border-neutral-800">
        {/* Left: Phone Contact */}
        <div className="flex-1 flex flex-col items-start border-b border-neutral-800 pb-4 sm:pb-0 sm:border-b-0 sm:border-r sm:border-neutral-800">
          <p className="text-gray-400 text-sm uppercase tracking-wide">TEL</p>
          <div className="flex items-center space-x-2 mt-2">
            <Phone className="w-5 h-5" />
            <span className="text-2xl font-semibold">03-4566-3208</span>
          </div>
          <p className="text-gray-400 text-sm mt-1">{t("businessHours")}</p>
          <p className="text-gray-400 text-sm">{t("closedDays")}</p>
        </div>

        {/* Right: Email Contact */}
        <div className="flex-1 flex flex-col items-start">
          <p className="text-gray-400 text-sm uppercase tracking-wide">CONTACT</p>
          <Link href="/inquiry" target="_blank" className="flex items-center space-x-2 mt-2">
            <Mail className="w-5 h-5" />
            <span className="text-2xl font-semibold">{t("contact")}</span>
            <span className="ml-2">
              <ArrowRight className="w-5 h-5" />
            </span>
          </Link>
          <p className="text-gray-400 text-sm mt-1">
            {t("contactDescription")}
          </p>
        </div>
      </div>

      {/* Navigation & Social Links */}
      <div className="max-w-6xl mx-auto px-6 py-4 flex flex-col md:flex-row justify-between items-center">
        {/* Links */}
        <nav className="text-sm space-x-4 flex flex-wrap justify-center">
          <span className="text-sm text-gray-400">{t("copyright")}</span>
          <Link href="/about" onClick={() => track("footer_click", { page: "about" })} className="text-gray-400 hover:text-white">{t("about")}</Link>
          <Link href="/team" onClick={() => track("footer_click", { page: "team" })} className="text-gray-400 hover:text-white">{t("team")}</Link>
          <Link href="https://jp.indeed.com/cmp/Tll%E5%90%88%E5%90%8C%E4%BC%9A%E7%A4%BE/jobs" target="_blank" onClick={() => track("footer_click", { page: "jobs" })} className="text-gray-400 hover:text-white flex items-center gap-1">
            <LinkIcon className="w-4 h-4" />
            {t("careers")}
          </Link>
        </nav>

        {/* Language Switcher & Social Icons */}
        <div className="flex flex-row items-center gap-4 mt-4 md:mt-0">
          <div className="flex items-center gap-2">
            <Globe className="w-4 h-4" />
            <select
              value={currentLocale}
              onChange={handleLanguageChange}
              className="bg-transparent text-white border border-gray-600 rounded px-2 py-1 text-sm focus:outline-none focus:border-white"
            >
              {routing.locales.map((locale) => (
                <option key={locale} value={locale} className="bg-black text-white">
                  {localeLabels[locale as keyof typeof localeLabels]}
                </option>
              ))}
            </select>
          </div>
          <div className="flex space-x-4">
            <Link href="https://www.facebook.com/urbalytics" target="_blank" onClick={() => track("footer_click", { page: "facebook" })}>
              <Facebook className="w-5 h-5 text-gray-400 hover:text-white cursor-pointer" />
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
