"use client";
import { Link } from "@/i18n/routing";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Phone, Globe } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { useEffect, useState } from "react";
import { track } from "@vercel/analytics";
import { useTranslations, useLocale } from "next-intl";
import { routing } from "@/i18n/routing";
import { usePathname } from "next/navigation";

export default function Header({ extraClass, isHomePage }: { extraClass?: string, isHomePage?: boolean }) {
  const t = useTranslations("Header");
  const [isScrolled, setIsScrolled] = useState(false);
  const currentLocale = useLocale();
  const pathname = usePathname();
  const localeLabels = {
    ja: "日本語",
    zh: "中文",
    en: "English"
  };

  const handleLanguageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newLocale = event.target.value;
    const newPath = pathname.replace(`/${currentLocale}`, `/${newLocale}`);
    window.location.href = newPath;
  };

  const handleScroll = () => {
    if (window.scrollY > window.innerHeight) {
      setIsScrolled(true);
    } else {
      setIsScrolled(false);
    }
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 w-full h-16 ${extraClass} flex flex-row justify-between items-center p-4 h-header-height ${isScrolled ? 'bg-white border-b border-gray-200' : ''}`}>

      <Link href="/" className="flex flex-row items-center gap-2 justify-center">
        <Image src="/assets/logo.png" alt="logo" width={32} height={32} />
        <span className={`text-lg font-bold text-blue-500 hidden sm:block`}>
          TLL
        </span>
      </Link>

      <div className={`py-2 text-sm sm:text-base flex flex-row gap-8 justify-center items-center ${isScrolled ? 'text-black' : ''}`}>
        <Link href="/about" onClick={() => track("header_click", { page: "about" })}>{t("about")}</Link>

        <Link href="/team" onClick={() => track("header_click", { page: "team" })}>{t("team")}</Link>

        <div className="flex items-center gap-2">
          <Globe
            className="w-4 h-4"
          />

          <select
            value={currentLocale}
            onChange={handleLanguageChange}
            className={`bg-transparent border border-gray-600 rounded px-2 py-1 text-sm focus:outline-none focus:border-white ${isScrolled || !isHomePage ? 'border-black text-black' : 'border-white text-white'}`}
          >
            {routing.locales.map((locale) => (
              <option key={locale} value={locale} className={`bg-black ${isScrolled ? 'text-black' : 'text-white'}`}>
                {localeLabels[locale as keyof typeof localeLabels]}
              </option>
            ))}
          </select>
        </div>
      </div>
    </header>
  );
}