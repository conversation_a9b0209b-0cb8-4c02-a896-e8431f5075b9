"use client";

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { Link } from "@/i18n/routing";
import clsx from "clsx";
import teamMembers from "@/app/[locale]/(subpages)/team/teamMembers";
import { useLocale, useTranslations } from "next-intl";

export default function TeamSection() {
  const locale = useLocale();
  const t = useTranslations("Team");
  // Show only core team members and limit to first 4 for homepage
  const coreTeamMembers = teamMembers[locale as keyof typeof teamMembers]?.filter((member: any) => member.type === "core").slice(0, 4);

  const renderTeamMembers = (members: any[]) => {
    return members?.map((member, index) => (
      <Card
        key={index}
        className={clsx(
          "transition-transform hover:scale-[1.02] hover:shadow-xl cursor-pointer",
          "rounded-2xl shadow-sm bg-white"
        )}
        onClick={() => {
          const urlName = member.nameEn.trim().toLowerCase().replace(/\s+/g, "-");
          window.location.href = `/team/${urlName}`;
        }}
      >
        <CardHeader className="flex flex-col items-center text-center">
          <div className="w-[100px] h-[100px] rounded-full overflow-hidden border border-gray-200 shadow-sm">
            <Image
              src={`/assets${member.image}`}
              alt={member.name}
              width={100}
              height={100}
              className="object-cover w-full h-full"
            />
          </div>
          <h3 className="mt-4 text-lg font-semibold">{member.name}</h3>
          <p className="text-gray-500 text-sm">{member.nameEn}</p>
          <p className="text-gray-700 font-medium text-sm">{member.title}</p>
        </CardHeader>

        <CardContent className="text-center space-y-2">
          <p className="text-sm text-gray-600 h-[80px] overflow-hidden text-left">
            {member.description.slice(0, 60)}...
          </p>
          {member.career && (
            <p className="text-xs text-gray-600 text-left">
              <b>{t("professional")}: </b>
              {member.career}
            </p>
          )}
        </CardContent>
      </Card>
    ));
  };

  return (
    <div className="max-w-6xl mx-auto py-12 px-4">
      <h2 className="text-center text-3xl font-bold mb-4">{t("title")}</h2>
      <Separator className="mb-6 w-24 mx-auto" />

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {renderTeamMembers(coreTeamMembers)}
      </div>

      {/* Call to Action */}
      <div className="text-center">
        <Link href="/team">
          <Button variant="outline" className="mt-4">
            {t("seeAll")}
          </Button>
        </Link>
      </div>
    </div>
  );
}
