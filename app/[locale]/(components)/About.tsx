"use client";

import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import { Link } from "@/i18n/routing";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

export default function AboutSection() {
  const t = useTranslations("About");

  return (
    <div className="max-w-6xl mx-auto py-12 px-4">
      <h2 className="text-center text-3xl font-bold mb-8">{t("title")}</h2>

      {/* Vision Section */}
      <div className="flex flex-col justify-between items-center w-full text-center p-4 py-6 gap-4 bg-gray-100 rounded-lg mb-8">
        <h3 className="text-xl sm:text-2xl font-bold text-center w-full">{t("vision")}</h3>
        <div className="text-lg">
          {t("visionText")}
        </div>

        {/* Mission Section */}
        <h3 className="text-xl sm:text-2xl font-bold text-center w-full mt-4">{t("mission")}</h3>
        <div className="space-y-6 text-left max-w-4xl">
          <div className="flex items-start space-x-3">
            <span className="text-4xl font-bold text-gray-600">1.</span>
            <div>
              <h4 className="text-lg font-semibold">{t("mission1Title")}</h4>
              <p className="text-gray-600 text-sm">
                {t("mission1Text")}
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <span className="text-4xl font-bold text-gray-600">2.</span>
            <div>
              <h4 className="text-lg font-semibold">{t("mission2Title")}</h4>
              <p className="text-gray-600 text-sm">
                {t("mission2Text")}
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <span className="text-4xl font-bold text-gray-600">3.</span>
            <div>
              <h4 className="text-lg font-semibold">{t("mission3Title")}</h4>
              <p className="text-gray-600 text-sm">
                {t("mission3Text")}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="text-center">
        <Link href="/about">
          <Button variant="outline" className="mt-4">
            {t("viewDetails")}
          </Button>
        </Link>
      </div>
    </div>
  );
}
