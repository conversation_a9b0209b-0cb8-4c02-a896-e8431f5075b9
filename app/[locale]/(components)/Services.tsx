"use client";

import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Link } from "@/i18n/routing";
import { useTranslations } from "next-intl";


export default function ServicesSection() {
  const t = useTranslations("Services");

  console.log(t("title"));

  const services = [
    {
      title: t("service1Title"),
      subtitle: t("service1Subtitle"),
      description: t("service1Description"),
      buttonLink: "#",
      image: "/services/bg4.avif",
    },
    {
      title: t("service2Title"),
      subtitle: t("service2Subtitle"),
      description: t("service2Description"),
      buttonLink: "#",
      image: "/services/bg3.avif",
    },
    {
      title: t("service3Title"),
      subtitle: t("service3Subtitle"),
      description: t("service3Description"),
      buttonLink: "#",
      image: "/services/bg2.avif",
    },
    {
      title: t("service4Title"),
      subtitle: t("service4Subtitle"),
      description: t("service4Description"),
      buttonLabel: t("seeSite"),
      url: "https://urbalytics.jp/",
      buttonLink: "#",
      image: "/services/4_urbalytics.avif",
    },
  ];


  return (
    <div className="max-w-6xl mx-auto py-12">
      <h2 className="text-center text-3xl font-bold mb-12">{t("title")}</h2>

      <div className="flex flex-col">
        {services.map((service, index) => (
          <div
            key={index}
            className={`flex flex-col md:flex-row ${index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"
              } items-center border-b border-gray-200 relative`}
          >
            {/* Image */}
            <div className="relative w-full md:w-1/2 h-[300px]">
              <Image
                src={`/assets${service.image}`}
                alt={service.title}
                layout="fill"
                objectFit="cover"
                className="px-6"
              />
            </div>

            {/* Text Content */}
            <div className={`w-full md:w-1/2 p-6 text-left sm:text-${index % 2 === 0 ? "left" : "right"}`}>
              <h3 className="text-2xl font-semibold">{service.title}</h3>
              <p className="text-blue-600 font-bold text-lg my-2">{service.subtitle}</p>
              <p className="text-neutral-900 text-base">{service.description.split(",").map((line, i) => (
                <span key={i}>
                  {line}
                  <br />
                </span>
              ))}
              </p>
              {service.url && (
                <Link href={service.url} target="_blank">
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => window.location.href = service.buttonLink}
                  >
                    {service.buttonLabel}
                  </Button>
                </Link>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
