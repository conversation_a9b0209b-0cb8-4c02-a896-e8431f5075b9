"use client";

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import Image from "next/image";
import { useTranslations } from "next-intl";

export default function StrengthsSection() {
  const t = useTranslations("Strength");

  const strengths = [
    {
      title: t("strength1Title"),
      description: t("strength1Description"),
      image: "/strength/1.jpg",
    },
    {
      title: t("strength2Title"),
      description: t("strength2Description"),
      image: "/strength/2.jpeg",
    },
    {
      title: t("strength3Title"),
      description: t("strength3Description"),
      image: "/strength/3.png",
    },
  ];

  return (
    <div className="max-w-6xl mx-auto py-12">
      <h2 className="text-center text-3xl font-bold mb-8">{t("title")}</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-4">
        {strengths.map((item, index) => (
          <Card key={index} className="shadow-md">
            <CardHeader className="relative h-52">
              <Image
                src={`/assets${item.image}`}
                alt={item.title}
                layout="fill"
                objectFit="cover"
                className="rounded-t-md"
              />
            </CardHeader>
            <CardContent className="p-6 text-center">
              <h3 className="text-lg font-semibold">{index + 1}. {item.title}</h3>
              <div className="text-gray-600 text-sm mt-2 text-left sm:text-center">{item.description}</div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
