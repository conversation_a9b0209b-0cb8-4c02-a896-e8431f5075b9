"use client";

import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";

export default function InquirySection() {
  const t = useTranslations("Inquiry");

  return (
    <div className="max-w-6xl mx-auto py-12 px-4">
      <h2 className="text-center text-3xl font-bold mb-4">{t("title")}</h2>

      <Separator />

      <div className="max-w-4xl mx-auto bg-white p-4">
        <div className="text-center mb-8">
          <p className="text-gray-600">{t("description")}</p>
        </div>

        {/* Embedded Inquiry Form */}
        <div className="w-full h-[1000px] border border-gray-200 rounded-lg overflow-hidden">
          <iframe
            src="https://tllrealestate.jp.larksuite.com/share/base/form/shrjpxV2eO4vo6RPghCzkd71XDh"
            name="inquiry-form"
            width="100%"
            height="100%"
            className="border-0"
          >
            インラインフレームを使用した部分です。
          </iframe>
        </div>
      </div>
    </div>
  );
}
