import { Metadata } from "next";
import { generateMetadata as generateSEOMetadata, pageMetadata } from '@/lib/metadata';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const teamPageData = pageMetadata.team(locale);
  
  return generateSEOMetadata(teamPageData);
}

export default function TeamLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
