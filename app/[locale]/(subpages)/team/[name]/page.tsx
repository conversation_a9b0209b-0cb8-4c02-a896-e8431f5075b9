"use client";

import { useParams } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import teamMembers from "../teamMembers";
import { Mail, Linkedin, Building2, GraduationCap, Award, FileText } from "lucide-react";
import BlogCard from "./blog/BlogCard";
import { useEffect, useState } from "react";
import { sanityClient } from "@/lib/sanityClient";
import { useTranslations } from "next-intl";

export default function TeamMemberProfile() {
  const params = useParams();
  const nameParam = typeof params.name === "string" ? params.name : Array.isArray(params.name) ? params.name[0] : "";
  const currentLocale = params.locale as string;
  const t = useTranslations("Team");

  const getMemberByUrlName = (urlName: string, currentLocaleめmべr: string) => {
    return teamMembers[currentLocale as keyof typeof teamMembers].find(
      (m: any) => m.nameEn.trim().toLowerCase().replace(/\s+/g, "-") === urlName
    );
  };

  const member = getMemberByUrlName(nameParam, currentLocale);

  if (!member) {
    return <div className="text-center py-20">メンバーが見つかりません。</div>;
  }

  const [blogs, setBlogs] = useState<any[]>([]);
  const [blogsSanity, setBlogsSanity] = useState<any[]>([]);

  const fetchBlogs = async () => {
    // Skip fetching if no writerId is provided
    if (!member.writerId) {
      console.log("No writerId provided for member:", member.name);
      return;
    }

    try {
      let writerId = member.writerId;

      const posts = await sanityClient.fetch(
        `*[_type == "post" && author == $writerId && defined(title_${currentLocale}) &&
        count(body_${currentLocale}) > 0]{
            _id, title_${currentLocale}, slug_${currentLocale}, publishedAt, body_${currentLocale}, image,
            category->{_id, name_${currentLocale}},
            "author": coalesce(author->name, "Urbalytics Team")
          }`,
        { writerId }
      );

      setBlogs(posts || []);
    } catch (error) {
      console.error("Error fetching blogs for", member.name, ":", error);
      setBlogs([]); // Set empty array on error
    }
  }

  useEffect(() => {
    fetchBlogs();
  }, []);

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <div className="border-b bg-card">
        <div className="max-w-6xl mx-auto px-4 py-16 md:py-20">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            {/* Profile Image */}
            <div className="flex-shrink-0">
              <div className="relative">
                <div className="w-80 h-80 overflow-hidden ">
                  <Image
                    src={`/assets${member.image}`}
                    alt={member.name}
                    width={320}
                    height={320}
                    className="w-full h-full object-cover"
                    priority
                  />
                </div>
              </div>
            </div>

            {/* Profile Info */}
            <div className="flex-1 text-center lg:text-left space-y-6">
              <div>
                <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-2">
                  {member.name}
                </h1>
                <p className="text-xl text-muted-foreground">{member.nameEn}</p>
              </div>

              <div>
                <Badge variant="secondary" className="text-lg px-4 py-2 font-medium">
                  {member.title}
                </Badge>
              </div>

              {/* Contact Links */}
              <div className="flex flex-wrap justify-center lg:justify-start gap-3">
                {(member as any).mail && (
                  <a
                    href={`mailto:${(member as any).mail}`}
                    className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <Mail className="w-4 h-4" />
                    <span>メール</span>
                  </a>
                )}
                {(member as any).linkedin && (
                  <a
                    href={(member as any).linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <Linkedin className="w-4 h-4" />
                    <span>LinkedIn</span>
                  </a>
                )}
              </div>

              {/* Career Highlight */}
              {member.career && (
                <div className="bg-muted/50 rounded-lg p-4 border">
                  <div className="flex items-center gap-2 mb-2">
                    <Building2 className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm font-medium text-muted-foreground">経歴</span>
                  </div>
                  <p className="text-foreground">{member.career}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content Sections */}
      <div className="max-w-6xl mx-auto px-4 py-16 space-y-8">
        {/* About Section */}
        <Card>
          <CardContent className="p-8">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                <FileText className="w-5 h-5 text-muted-foreground" />
              </div>
              <h2 className="text-2xl font-semibold text-foreground">紹介</h2>
            </div>
            <Separator className="mb-6" />
            <div className="prose prose-neutral max-w-none">
              <p className="text-muted-foreground leading-relaxed whitespace-pre-line">
                {member.description}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Education & Certificates Grid */}
        <div className="grid grid-cols-1 gap-6">
          {/* Education Section */}
          {member.education && Array.isArray(member.education) && member.education.length > 0 && (
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                    <GraduationCap className="w-5 h-5 text-muted-foreground" />
                  </div>
                  <h2 className="text-xl font-semibold text-foreground">学歴</h2>
                </div>
                <Separator className="mb-4" />
                <div className="space-y-3">
                  {member.education.map((edu: string, index: number) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="w-1.5 h-1.5 bg-muted-foreground rounded-full mt-2 flex-shrink-0"></div>
                      <p className="text-muted-foreground">{edu}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Certificates Section */}
          {member.certificate && member.certificate.length > 0 && (
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                    <Award className="w-5 h-5 text-muted-foreground" />
                  </div>
                  <h2 className="text-xl font-semibold text-foreground">{t("certificate")}</h2>
                </div>
                <Separator className="mb-4" />
                <div className="space-y-2">
                  {member.certificate.map((cert, index) => (
                    <div key={index}>
                      <Badge variant="outline" className="text-sm">
                        {cert}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Blog Articles Section */}
        {blogs.length > 0 && (
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-muted-foreground" />
                </div>
                <h2 className="text-2xl font-semibold text-foreground">執筆記事</h2>
              </div>
              <Separator className="mb-6" />
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {blogs.map((blog: any) => (
                  <BlogCard key={blog._id} post={blog} currentLocale={currentLocale} postLocalDb={blogsSanity} />
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
