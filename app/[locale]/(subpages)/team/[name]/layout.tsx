import { Metadata } from "next";
import { generateMetadata as generateSEOMetadata, pageMetadata } from '@/lib/metadata';
import { getMessages } from "next-intl/server";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; name: string }>;
}): Promise<Metadata> {
  const { locale, name } = await params;

  try {
    const messages = await getMessages();
    const teamMessages = messages.Team as any;

    if (!teamMessages?.members) {
      throw new Error('Team members data not found');
    }

    // Find the team member by URL name
    const memberKey = Object.keys(teamMessages.members).find(key => {
      const member = teamMessages.members[key];
      const urlName = member.nameEn.trim().toLowerCase().replace(/\s+/g, "-");
      return urlName === name;
    });

    if (!memberKey) {
      // Fallback metadata if member not found
      return generateSEOMetadata({
        title: locale === 'en' ? 'Team Member Not Found - TLL' :
               locale === 'zh' ? '未找到团队成员 - TLL' :
               'チームメンバーが見つかりません - TLL',
        description: locale === 'en' ? 'The requested team member could not be found.' :
                     locale === 'zh' ? '找不到请求的团队成员。' :
                     'リクエストされたチームメンバーが見つかりませんでした。',
        noindex: true,
      });
    }

    const member = teamMessages.members[memberKey];
    const memberPageData = pageMetadata.teamMember(locale, member.name, member.title);

    return generateSEOMetadata({
      ...memberPageData,
      ogImage: `/assets/team/${memberKey}.png`,
    });
  } catch (error) {
    // Fallback metadata if there's any error
    return generateSEOMetadata({
      title: locale === 'en' ? 'Team Member - TLL' :
             locale === 'zh' ? '团队成员 - TLL' :
             'チームメンバー - TLL',
      description: locale === 'en' ? 'Meet our team member at TLL.' :
                   locale === 'zh' ? '认识我们TLL的团队成员。' :
                   'TLLのチームメンバーをご紹介します。',
    });
  }
}

export default function TeamMemberLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
