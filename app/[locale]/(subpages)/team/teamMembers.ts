const teamMembersJp = [
  {
    name: "譚　琛",
    nameEn: "<PERSON>",
    title: "創業者",
    description: "日本および中華圏の金融・不動産業界において、10年以上にわたって組織設計やデジタル変革を主導。その後、大手ECで主力商品のプロダクトデザインを担当",
    career: "マッキンゼー・アンド・コンパニー, アマゾン",
    education: ["シンガポール国立大学 コンピュータ工学", "IEビジネススクール MBA"],
    image: "/team/chen.png",
    type: "core",
    certificate: ["宅地建物取引士", "ビル経営管理士", "米国認定バイヤーズエージェン"],
    writerId: "chen-tan",
  },
  {
    name: "キャサリン　ルー",
    nameEn: "Catherine Lu",
    title: "創業者 / CIO",
    description: "機関投資家向け不動産投資および資産管理において、10年以上にわたってさまざまな資産クラスと投資戦略に従事。3000億円以上の不動産取引や売却を主導し、1兆円以上の総運用資産（AUM）を管理を経験",
    career: "ローンスターファンド, ベインキャピタル",
    education: ["シンガポール国立大学, 統計学卒業"],
    image: "/team/lu.png",
    type: "core",
    writerId: "qingqing-lu",
  },
  {
    name: "羅 興开",
    nameEn: "Xingkai Luo",
    title: "創業者 / COO",
    description: "交通・物流業界向け都市不動産開発プロジェクトの指揮を7年以上経験。中国でのEC大手AlibabaとHoneywellでプロジェクトマネージャーを務め、大手国有交通事業者のプロジェクト開発や中国の大手不動産デベロッパーのインテリジェントビルソリューションの展開を主導",
    career: "アリババ",
    education: ["北京交通大学 産業工学修士", "米国ミズーリ大学 MBA"],
    image: "/team/luo.png",
    type: "core",
    writerId: "xingkai-luo",
  },
  {
    name: "李 瀟",
    nameEn: "Xiao Li",
    title: "アセットマネジメント",
    description: "日本のIT大手で8年間にわたりプロジェクトマネージャーとして活躍し、企業DX化事業を手掛ける。自社独自のシステムUrbalyticsを構築し、買取相場や販売相場、賃貸相場、地域データなどのビックデータを解析することで、買取再販事業の立ち上げ及び推進を担当",
    career: "NTTコミュニケーションズ",
    education: ["早稲田大学、情報通信修士卒業"],
    image: "/team/li.png",
    type: "core",
    certificate: ["宅地建物取引士"],
    writerId: "xiao-li",
  },
  // {
  //   name: "小林 旭",
  //   nameEn: "Akira Kobayashi",
  //   title: "仕入 / 仲介",
  //   description: "国内外の富裕層の顧客向けに高級不動産の仲介を行う会社に従事後、TLLに参画。国内にいるエグゼクティブ向けに賃貸・売買仲介及び管理を約8年経験。",
  //   career: "ケン・コーポレーション",
  //   education: "",
  //   image: "/team/kobayashi.png",
  //   type: "core",
  //   certificate: ["宅地建物取引士", "賃貸不動産経営管理士"],
  // },
  {
    name: "アバクン",
    nameEn: "Urba Kun",
    title: "CEO（Chief Emotional Officer）",
    description: "長年にわたってチームの癒しと警備を担当。来客対応（吠えるだけ）、散歩中の市場調査、そしておやつの品評において圧倒的なリーダーシップを発揮。本人曰く、TLLの真のCEO（Chief Entertainment Officer）だとか",
    career: "TLL",
    education: "",
    image: "/team/man.jpg",
    type: "core",
  },
  {
    name: "夏目 皆実",
    nameEn: "Natsume Minami",
    title: "",
    description: "外資系保険会社にてセールスマネージャーとして不動産に関連する保険サービスの販売に7年以上経験。 その後、自ら保険代理店を設立し、在日外国人の富裕層向けに総合的な資産計画・リスク管理サービスを提供",
    career: "",
    education: "",
    image: "/team/partners/xia.png",
    type: "partner",
  },
  {
    name: "許 皆歓",
    nameEn: "Ralph Xu",
    title: "",
    description: "外資系法律事務所で弁護士として勤務経験があり、世界500強企業で法務を担当。来日後、不動産投資に6年以上従事し、自身の投資経験と大家の視点を活かし、外国人投資者向けに日本不動産投資の仲介・コンサルティングサービスを提供しています",
    career: "",
    education: "ワシントン大学法学院 法律修士",
    image: "/team/partners/xu.jpg",
    type: "partner",
  },
] as {
  name: string;
  nameEn: string;
  title: string;
  description: string;
  career: string;
  education: string[];
  image: string;
  type: string;
  mail?: string;
  linkedin?: string;
  certificate?: string[];
  writerId?: string;
}[];


const teamMembersEn = [
  {
    name: "Chen Tan",
    nameEn: "Chen Tan",
    title: "Founder",
    description: "Led organizational design and digital transformation in Japan and Greater China's financial and real estate industries for over 10 years. Subsequently responsible for product design of core products at major e-commerce companies",
    career: "McKinsey & Company, Amazon",
    education: ["National University of Singapore Computer Engineering", "IE Business School MBA"],
    image: "/team/chen.png",
    type: "core",
    certificate: ["Real Estate Transaction Specialist", "Building Management Specialist", "US Certified Buyer's Agent"],
    writerId: "chen-tan",
  },
  {
    name: "Catherine Lu",
    nameEn: "Catherine Lu",
    title: "Founder / CIO",
    description: "Engaged in various asset classes and investment strategies in institutional real estate investment and asset management for over 10 years. Led real estate transactions and sales exceeding 300 billion yen and managed total assets under management (AUM) of over 1 trillion yen",
    career: "Lone Star Fund, Bain Capital",
    education: ["National University of Singapore, Statistics Graduate"],
    image: "/team/lu.png",
    type: "core",
    writerId: "qingqing-lu",
  },
  {
    name: "Xingkai Luo",
    nameEn: "Xingkai Luo",
    title: "Founder / COO",
    description: "Over 7 years of experience leading urban real estate development projects for transportation and logistics industries. Served as project manager at Chinese e-commerce giant Alibaba and Honeywell, leading project development for major state-owned transportation companies and intelligent building solution deployment for China's major real estate developers",
    career: "Alibaba",
    education: ["Beijing Jiaotong University Industrial Engineering Master", "University of Missouri MBA"],
    image: "/team/luo.png",
    type: "core",
    writerId: "xingkai-luo",
  },
  {
    name: "Xiao Li",
    nameEn: "Xiao Li",
    title: "Asset Management",
    description: "Actively worked as project manager at major Japanese IT companies for 8 years, handling corporate digital transformation projects. Built proprietary Urbalytics system, responsible for launching and advancing acquisition and resale business by analyzing big data including acquisition rates, sales rates, rental rates, and regional data",
    career: "NTT Communications",
    education: ["Waseda University Information and Communications Engineering Master Graduate"],
    image: "/team/li.png",
    type: "core",
    certificate: ["Real Estate Transaction Specialist"],
    writerId: "xiao-li",
  },
  // {
  //   name: "Akira Kobayashi",
  //   nameEn: "Akira Kobayashi",
  //   title: "Acquisition / Brokerage",
  //   description: "After working at a company handling high-end real estate brokerage for domestic and international high-net-worth clients, joined TLL. Approximately 8 years of experience in rental and sales brokerage and management for executives in Japan",
  //   career: "Ken Corporation",
  //   education: "",
  //   image: "/team/kobayashi.png",
  //   type: "core",
  //   certificate: ["Real Estate Transaction Specialist", "Rental Real Estate Management Specialist"],
  // },
  {
    name: "Urba Kun",
    nameEn: "Urba Kun",
    title: "CEO (Chief Emotional Officer)",
    description: "Responsible for team healing and security for many years. Demonstrates overwhelming leadership in guest reception (barking only), market research during walks, and snack evaluation. According to himself, he is TLL's true CEO (Chief Entertainment Officer)",
    career: "TLL",
    education: "",
    image: "/team/man.jpg",
    type: "core",
  },
  {
    name: "Natsume Minami",
    nameEn: "Natsume Minami",
    title: "",
    description: "Over 7 years of experience as sales manager at foreign insurance companies selling insurance services related to real estate. Subsequently established own insurance agency, providing comprehensive asset planning and risk management services for high-net-worth foreigners in Japan",
    career: "",
    education: "",
    image: "/team/partners/xia.png",
    type: "partner",
  },
  {
    name: "Ralph Xu",
    nameEn: "Ralph Xu",
    title: "",
    description: "Worked as lawyer at foreign law firms and handled legal affairs at Fortune 500 companies. After coming to Japan, engaged in real estate investment for over 6 years, providing brokerage and consulting services for Japanese real estate investment to foreign investors utilizing own investment experience and landlord perspective",
    career: "",
    education: "Washington University School of Law Legal Master",
    image: "/team/partners/xu.jpg",
    type: "partner",
  },
]

const teamMembersZh = [
  {
    name: "谭琛",
    nameEn: "Chen Tan",
    title: "创始人",
    description: "在日本及中华圈金融房地产行业，主导组织设计和数字化转型超过10年。之后，在大型电商平台负责主力产品的产品设计",
    career: "麦肯锡公司, 亚马逊",
    education: ["新加坡国立大学 计算机工程", "IE商学院 MBA"],
    image: "/team/chen.png",
    type: "core",
    certificate: ["宅地建物交易士", "建筑经营管理士", "美国认证买家代理"],
    writerId: "chen-tan",
  },
  {
    name: "陆青青",
    nameEn: "Catherine Lu",
    title: "创始人 / CIO",
    description: "在机构投资者房地产投资和资产管理领域，从事各种资产类别和投资策略超过10年。主导超过3000亿日元的房地产交易和出售，管理超过1万亿日元的总管理资产（AUM）",
    career: "朗星基金, 贝恩资本",
    education: ["新加坡国立大学 统计学毕业"],
    image: "/team/lu.png",
    type: "core",
    writerId: "qingqing-lu",
  },
  {
    name: "罗兴开",
    nameEn: "Xingkai Luo",
    title: "创始人 / COO",
    description: "在交通物流行业城市房地产开发项目指挥方面有7年以上经验。在中国电商巨头阿里巴巴和霍尼韦尔担任项目经理，主导大型国有交通企业的项目开发和中国大型房地产开发商智能建筑解决方案的推广",
    career: "阿里巴巴",
    education: ["北京交通大学 产业工程硕士", "美国密苏里大学 MBA"],
    image: "/team/luo.png",
    type: "core",
    writerId: "xingkai-luo",
  },
  {
    name: "李潇",
    nameEn: "Xiao Li",
    title: "资产管理",
    description: "在日本IT巨头担任项目经理8年，负责企业数字化转型业务。构建公司独有的Urbalytics系统，通过分析收购行情、销售行情、租赁行情、地区数据等大数据，负责收购再销售业务的启动和推进",
    career: "NTT通信",
    education: ["早稻田大学 信息通信硕士毕业"],
    image: "/team/li.png",
    type: "core",
    certificate: ["宅地建物交易士"],
    writerId: "xiao-li",
  },
  // {
  //   name: "小林旭",
  //   nameEn: "Akira Kobayashi",
  //   title: "采购 / 中介",
  //   description: "在面向国内外富裕阶层客户的高端房地产中介公司工作后，加入TLL。为国内高管提供租赁买卖中介及管理服务约8年经验",
  //   career: "肯公司",
  //   education: "",
  //   image: "/team/kobayashi.png",
  //   type: "core",
  //   certificate: ["宅地建物交易士", "租赁房地产经营管理士"],
  // },
  {
    name: "阿巴昆",
    nameEn: "Urba Kun",
    title: "CEO（首席情感官）",
    description: "多年来负责团队的治愈和安保工作。在访客接待（只会叫）、散步时的市场调查、以及零食品评方面发挥压倒性的领导力。据本人说，是TLL真正的CEO（首席娱乐官）",
    career: "TLL",
    education: "",
    image: "/team/man.jpg",
    type: "core",
  },
  {
    name: "夏目皆实",
    nameEn: "Natsume Minami",
    title: "",
    description: "在外资保险公司担任销售经理，从事房地产相关保险服务销售7年以上。之后，自己设立保险代理店，为在日外国人富裕阶层提供综合性资产规划和风险管理服务",
    career: "",
    education: "",
    image: "/team/partners/xia.png",
    type: "partner",
  },
  {
    name: "许皆欢",
    nameEn: "Ralph Xu",
    title: "",
    description: "在外资律师事务所担任律师，在世界500强企业负责法务。来日后，从事房地产投资6年以上，利用自己的投资经验和房东视角，为外国投资者提供日本房地产投资中介和咨询服务",
    career: "",
    education: "华盛顿大学法学院 法律硕士",
    image: "/team/partners/xu.jpg",
    type: "partner",
  },
]

export default {
  zh: teamMembersZh,
  en: teamMembersEn,
  ja: teamMembersJp,
};
