"use client";

import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import clsx from "clsx";
import teamMembers from "./teamMembers";
import { useRouter } from "@/i18n/routing";
import { useParams } from "next/navigation";
import { useTranslations } from "next-intl";

export default function TeamPage() {
  const t = useTranslations("Team");
  const sections = [
    { title: t("coreTeam"), type: "core" },
    { title: t("partners"), type: "partner" },
  ];



  const router = useRouter();
  const params = useParams();
  const locale = params.locale as string;
  const renderTeamMembers = (members: any[]) => {
    return members?.map((member, index) => (
      <Card
        key={index}
        className={clsx(
          "transition-transform hover:scale-[1.02] hover:shadow-xl cursor-pointer",
          "rounded-2xl shadow-sm bg-white flex flex-col"
        )}
        onClick={() => {
          const urlName = member.nameEn.trim().toLowerCase().replace(/\s+/g, "-");
          router.push(`/team/${urlName}`);
        }}
      >
        <CardHeader className="flex flex-col items-center text-center">
          <div className="w-[120px] h-[120px] rounded-full overflow-hidden border border-gray-200 shadow-sm">
            <Image
              src={`/assets${member.image}`}
              alt={member.name}
              width={120}
              height={120}
              className="object-cover w-full h-full"
            />
          </div>
          <h3 className="mt-4 text-xl font-semibold">{member.name}</h3>
          <p className="text-gray-500 text-sm">{member.nameEn}</p>
          <p className="text-gray-700 font-medium">{member.title}</p>
        </CardHeader>

        <CardContent className="text-center space-y-2 text-left flex flex-col gap-2 flex-1">
          <p className="text-sm text-gray-600 flex-1">
            {member.description}
          </p>

          <div className="flex flex-col gap-2 mt-auto">
            {member.career && (
              <p className="text-xs text-gray-600 text-left">
                <b>{t("professional")}: </b>
                {member.career}
              </p>
            )}
            {member.education && (
              <p className="text-xs text-gray-600 whitespace-pre-line text-left">
                <b>{t("education")}: </b>
                {member.education}
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    ));
  };

  return (
    <div className="max-w-6xl mx-auto py-12 px-4 space-y-24">
      {sections.map((section) => (
        <div key={section.type}>
          <h2 className="text-center text-3xl font-bold mb-4">{section.title}</h2>
          <Separator className="mb-6 w-24 mx-auto" />
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {renderTeamMembers(
              teamMembers[locale as keyof typeof teamMembers]?.filter((member: any) => member.type === section.type) || []
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
