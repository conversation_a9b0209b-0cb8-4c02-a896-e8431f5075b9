import { Metadata } from "next";
import { generateMetadata as generateSEOMetadata, pageMetadata } from '@/lib/metadata';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const agentPageData = pageMetadata.agent(locale);
  
  return generateSEOMetadata(agentPageData);
}

export default function AgentLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
