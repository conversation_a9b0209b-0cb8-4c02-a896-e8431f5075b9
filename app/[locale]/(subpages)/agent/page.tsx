"use client";

import { useTranslations } from "next-intl";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  ArrowRight,
  CheckCircle,
  Users,
  TrendingUp,
  Clock,
  Shield,
  Zap,
  Target,
} from "lucide-react";

export default function AgentPage() {
  const t = useTranslations("Agent");

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 to-blue-800 text-white py-20">
        <div className="max-w-6xl mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            {t("heroTitle")}
          </h1>
          <p className="text-xl md:text-2xl mb-8 opacity-90">
            {t("heroSubtitle")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Button
              size="lg"
              className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg"
            >
              {t("ctaButton")}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>

          {/* Hero Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">75%</div>
              <div className="text-lg opacity-90">{t("commissionRate")}</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">5.3{t("hours")}</div>
              <div className="text-lg opacity-90">{t("averageWorkHours")}</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">{t("noQuota")}</div>
              <div className="text-lg opacity-90">{t("freeWorkStyle")}</div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Overview */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              {t("benefitsTitle")}
            </h2>
            <p className="text-xl text-gray-600">{t("benefitsSubtitle")}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {t("benefit1Title")}
              </h3>
              <p className="text-gray-600 text-sm">
                {t("benefit1Description")}
              </p>
            </Card>

            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {t("benefit2Title")}
              </h3>
              <p className="text-gray-600 text-sm">
                {t("benefit2Description")}
              </p>
            </Card>

            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {t("benefit3Title")}
              </h3>
              <p className="text-gray-600 text-sm">
                {t("benefit3Description")}
              </p>
            </Card>

            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-8 w-8 text-orange-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {t("benefit4Title")}
              </h3>
              <p className="text-gray-600 text-sm">
                {t("benefit4Description")}
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* Commission Calculator */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              {t("calculatorTitle")}
            </h2>
            <p className="text-xl text-gray-600">{t("calculatorSubtitle")}</p>
          </div>

          <Card className="p-8 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <Image
                  src="https://placehold.jp/400x300/4F46E5/FFFFFF?text=Commission+Calculator"
                  alt="Commission Calculator"
                  width={400}
                  height={300}
                  className="rounded-lg shadow-md"
                />
              </div>
              <div className="space-y-6">
                <div className="flex justify-between items-center py-3 border-b">
                  <span className="font-medium">
                    {t("averagePropertyPrice")}
                  </span>
                  <span className="text-xl font-bold">4,000{t("manYen")}</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b">
                  <span className="font-medium">{t("averageCommission")}</span>
                  <span className="text-xl font-bold">4%</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b">
                  <span className="font-medium">{t("annualDeals")}</span>
                  <span className="text-xl font-bold">10{t("deals")}</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b">
                  <span className="font-medium">{t("commissionRate")}</span>
                  <span className="text-xl font-bold text-blue-600">75%</span>
                </div>
                <Separator />
                <div className="flex justify-between items-center py-3">
                  <span className="text-lg font-semibold">
                    {t("yourAnnualIncome")}
                  </span>
                  <span className="text-2xl font-bold text-green-600">
                    1,200{t("manYen")}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              {t("whyChooseTitle")}
            </h2>
            <p className="text-xl text-gray-600">{t("whyChooseSubtitle")}</p>
          </div>

          <div className="space-y-16">
            {/* Reason 1 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <Badge className="mb-4 bg-blue-100 text-blue-800">
                  {t("reason1Badge")}
                </Badge>
                <h3 className="text-2xl font-bold mb-4">{t("reason1Title")}</h3>
                <p className="text-gray-600 mb-6">{t("reason1Description")}</p>
                <ul className="space-y-3">
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>{t("reason1Point1")}</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>{t("reason1Point2")}</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>{t("reason1Point3")}</span>
                  </li>
                </ul>
              </div>
              <div>
                <Image
                  src="https://placehold.jp/500x400/3B82F6/FFFFFF?text=Technology+Platform"
                  alt="Technology Platform"
                  width={500}
                  height={400}
                  className="rounded-lg shadow-lg"
                />
              </div>
            </div>

            {/* Reason 2 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                <Image
                  src="https://placehold.jp/500x400/10B981/FFFFFF?text=Lead+Generation"
                  alt="Lead Generation"
                  width={500}
                  height={400}
                  className="rounded-lg shadow-lg"
                />
              </div>
              <div className="order-1 lg:order-2">
                <Badge className="mb-4 bg-green-100 text-green-800">
                  {t("reason2Badge")}
                </Badge>
                <h3 className="text-2xl font-bold mb-4">{t("reason2Title")}</h3>
                <p className="text-gray-600 mb-6">{t("reason2Description")}</p>
                <ul className="space-y-3">
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>{t("reason2Point1")}</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>{t("reason2Point2")}</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>{t("reason2Point3")}</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Reason 3 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <Badge className="mb-4 bg-purple-100 text-purple-800">
                  {t("reason3Badge")}
                </Badge>
                <h3 className="text-2xl font-bold mb-4">{t("reason3Title")}</h3>
                <p className="text-gray-600 mb-6">{t("reason3Description")}</p>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white p-4 rounded-lg shadow-sm">
                    <div className="text-2xl font-bold text-purple-600 mb-1">
                      24/7
                    </div>
                    <div className="text-sm text-gray-600">
                      {t("support247")}
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-lg shadow-sm">
                    <div className="text-2xl font-bold text-purple-600 mb-1">
                      100+
                    </div>
                    <div className="text-sm text-gray-600">
                      {t("supportTools")}
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <Image
                  src="https://placehold.jp/500x400/8B5CF6/FFFFFF?text=Support+Team"
                  alt="Support Team"
                  width={500}
                  height={400}
                  className="rounded-lg shadow-lg"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            {t("ctaTitle")}
          </h2>
          <p className="text-xl mb-8 opacity-90">{t("ctaDescription")}</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg"
            >
              {t("ctaButton")}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>
          <div className="mt-8 text-sm opacity-75">{t("ctaNote")}</div>
        </div>
      </section>

      {/* Requirements Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              {t("requirementsTitle")}
            </h2>
            <p className="text-xl text-gray-600">{t("requirementsSubtitle")}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="p-6">
              <h3 className="text-xl font-semibold mb-4 text-red-600">
                {t("requiredTitle")}
              </h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-red-500 mr-3 mt-0.5" />
                  <span>{t("required1")}</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-red-500 mr-3 mt-0.5" />
                  <span>{t("required2")}</span>
                </li>
              </ul>
            </Card>

            <Card className="p-6">
              <h3 className="text-xl font-semibold mb-4 text-green-600">
                {t("preferredTitle")}
              </h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3 mt-0.5" />
                  <span>{t("preferred1")}</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3 mt-0.5" />
                  <span>{t("preferred2")}</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3 mt-0.5" />
                  <span>{t("preferred3")}</span>
                </li>
              </ul>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
}
