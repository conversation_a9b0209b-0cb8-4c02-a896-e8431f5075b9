"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";

interface TestimonialCardProps {
  name: string;
  title: string;
  image: string;
  quote: string;
  tags: string[];
}

export default function TestimonialCard({ name, title, image, quote, tags }: TestimonialCardProps) {
  return (
    <Card className="p-6 hover:shadow-lg transition-shadow">
      <CardContent className="p-0">
        <div className="flex items-start space-x-4 mb-4">
          <Image
            src={image}
            alt={name}
            width={60}
            height={60}
            className="rounded-full object-cover"
          />
          <div>
            <h3 className="font-semibold text-lg">{name}</h3>
            <p className="text-gray-600 text-sm">{title}</p>
          </div>
        </div>
        
        <blockquote className="text-gray-700 mb-4 italic">
          "{quote}"
        </blockquote>
        
        <div className="flex flex-wrap gap-2">
          {tags.map((tag, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              #{tag}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
