"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle } from "lucide-react";
import Image from "next/image";

interface FeatureHighlightProps {
  badge: string;
  title: string;
  description: string;
  points: string[];
  image: string;
  imageAlt: string;
  reversed?: boolean;
}

export default function FeatureHighlight({
  badge,
  title,
  description,
  points,
  image,
  imageAlt,
  reversed = false
}: FeatureHighlightProps) {
  const contentSection = (
    <div className={`${reversed ? 'order-1 lg:order-2' : ''}`}>
      <Badge className="mb-4 bg-blue-100 text-blue-800">
        {badge}
      </Badge>
      <h3 className="text-2xl font-bold mb-4">{title}</h3>
      <p className="text-gray-600 mb-6">{description}</p>
      <ul className="space-y-3">
        {points.map((point, index) => (
          <li key={index} className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
            <span>{point}</span>
          </li>
        ))}
      </ul>
    </div>
  );

  const imageSection = (
    <div className={`${reversed ? 'order-2 lg:order-1' : ''}`}>
      <Image
        src={image}
        alt={imageAlt}
        width={500}
        height={400}
        className="rounded-lg shadow-lg w-full h-auto"
      />
    </div>
  );

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      {contentSection}
      {imageSection}
    </div>
  );
}
