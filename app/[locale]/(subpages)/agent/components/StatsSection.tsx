"use client";

import { Card } from "@/components/ui/card";

interface StatItemProps {
  value: string;
  label: string;
  description?: string;
}

interface StatsSectionProps {
  title: string;
  subtitle?: string;
  stats: StatItemProps[];
}

function StatItem({ value, label, description }: StatItemProps) {
  return (
    <Card className="p-6 text-center hover:shadow-lg transition-shadow">
      <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">
        {value}
      </div>
      <div className="text-lg font-medium mb-1">{label}</div>
      {description && (
        <div className="text-sm text-gray-600">{description}</div>
      )}
    </Card>
  );
}

export default function StatsSection({ title, subtitle, stats }: StatsSectionProps) {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">{title}</h2>
          {subtitle && (
            <p className="text-xl text-gray-600">{subtitle}</p>
          )}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {stats.map((stat, index) => (
            <StatItem
              key={index}
              value={stat.value}
              label={stat.label}
              description={stat.description}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
