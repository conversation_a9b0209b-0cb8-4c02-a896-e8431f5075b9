import { Metadata } from "next";
import { generateMetadata as generateSEOMetadata, pageMetadata } from '@/lib/metadata';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const inquiryPageData = pageMetadata.inquiry(locale);

  return generateSEOMetadata(inquiryPageData);
}

export default function InquiryPage() {
  return (
    <div className="max-w-6xl mx-auto py-12 px-4 h-[calc(100vh-var(--header-height))]">
      <iframe
        src="https://tllrealestate.jp.larksuite.com/share/base/form/shrjpxV2eO4vo6RPghCzkd71XDh"
        name="sample"
        width="100%"
        height="100%"
      >
        インラインフレームを使用した部分です。
      </iframe>
    </div>
  );
}
