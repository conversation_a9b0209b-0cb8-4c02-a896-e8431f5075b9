import Header from "@/app/Header";
import { Separator } from "@/components/ui/separator";
import CompanyOverview from "./Table";
import Image from "next/image";
import CompanyHistory from "./History";
import Access from "./Access";
import { useTranslations } from "next-intl";
import { Metadata } from "next";
import { generateMetadata as generateSEOMetadata, pageMetadata } from '@/lib/metadata';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const aboutPageData = pageMetadata.about(locale);

  return generateSEOMetadata(aboutPageData);
}

export default function About() {
  const t = useTranslations("About");

  return (
    <div className="flex flex-col justify-between items-center">
      <div className="flex flex-row justify-between items-center w-full text-center py-6">
        <h1 className="text-2xl sm:text-4xl font-bold text-center w-full">{t("title")}</h1>
      </div>
      <Separator className="w-full" />

      <div className="flex flex-col justify-between items-center w-full text-center p-4 py-6 gap-4 bg-gray-100">
        <h2 className="text-xl sm:text-2xl font-bold text-center w-full">{t("vision")}</h2>
        <div>
          {t("visionText")}
        </div>
        <Image src="/assets/bg1.avif" alt="Background"
          width={1920}
          height={1080}
          className="mx-auto sm:max-w-lg"
        />

        <h2 className="text-xl sm:text-2xl font-bold text-center w-full mt-4">{t("mission")}</h2>
        <div className="space-y-6 text-left">
          <div className="flex items-start space-x-3">
            <span className="text-4xl font-bold text-gray-600">1.</span>
            <div>
              <h3 className="text-lg font-semibold">{t("mission1Title")}</h3>
              <p className="text-gray-600 text-sm">
                {t("mission1Text")}
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <span className="text-4xl font-bold text-gray-600">2.</span>
            <div>
              <h3 className="text-lg font-semibold">{t("mission2Title")}</h3>
              <p className="text-gray-600 text-sm">
                {t("mission2Text")}
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <span className="text-4xl font-bold text-gray-600">3.</span>
            <div>
              <h3 className="text-lg font-semibold">{t("mission3Title")}</h3>
              <p className="text-gray-600 text-sm">
                {t("mission3Text")}
              </p>
            </div>
          </div>
        </div>
      </div>

      <Separator className="w-full" />

      <CompanyOverview />

      <Separator className="w-full" />

      <CompanyHistory />

      <Separator className="w-full" />

      <Access />
    </div>
  );
}
