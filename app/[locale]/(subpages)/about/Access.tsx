import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";

export default function Access() {
  const t = useTranslations("About");

  return (
    <div className="w-full pb-[120px] text-center flex flex-col items-center">
      <h2 className="text-xl sm:text-2xl font-bold text-center w-full p-6">
        {t("access")}
      </h2>

      <Separator className="w-full mb-4" />

      <div className="flex flex-col gap-4 p-4">
        <p>
          <b>{t("accessLocation")}：</b>
          {t("accessLocationDetails")}
        </p>

        <div className="flex flex-row gap-2 items-center">
          <b>{t("accessTransport")}：</b>

          <div className="flex flex-col gap-2 items-center">
            {t("accessTransportDetails")?.split(",").map((item, index) => (
              <div key={index} >{item}</div>
            ))}
          </div>
        </div>
      </div>

      <iframe
        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.8650333821197!2d139.68984471263263!3d35.65569677248131!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188b5e3b92b501%3A0x383aa1cd3cce9a6b!2zVExM5ZCI5ZCM5Lya56S-!5e0!3m2!1sja!2sjp!4v1715413133103!5m2!1sja!2sjp"
        width="100%"
        style={{ border: 0 }}
        height="800"
        name="company"
        allowFullScreen
        loading="lazy"
        referrerPolicy="no-referrer-when-downgrade"
        className="w-full h-[800px] py-8"
      >
        TLL地図
      </iframe>
    </div>
  );
}
