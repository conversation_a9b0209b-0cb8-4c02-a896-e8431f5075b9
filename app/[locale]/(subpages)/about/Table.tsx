"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";

export default function CompanyOverview() {
  const t = useTranslations("About");

  return (
    <div className="max-w-4xl mx-auto my-4 p-4">
      <h2 className="text-xl sm:text-2xl font-bold text-center w-full">
        {t("companyOverview")}
      </h2>

      <Separator className="w-full my-4" />

      <Table>
        <TableBody>
          <TableRow>
            <TableCell className="font-medium w-[140px]">{t("companyNameTitle")}</TableCell>
            <TableCell>{t("companyNameContent")}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">{t("companyLocationTitle")}</TableCell>
            <TableCell>{t("companyLocation")}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">{t("companyEstablishedTitle")}</TableCell>
            <TableCell>{t("companyEstablished")}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">{t("companyFinancialYearTitle")}</TableCell>
            <TableCell>{t("companyFinancialYear")}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">{t("companyCapitalTitle")}</TableCell>
            <TableCell>{t("companyCapital")}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">{t("menkyoTitle")}</TableCell>
            <TableCell>{t("menkyo")}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">{t("businessTitle")}</TableCell>
            <TableCell>
              <ul className="list-disc pl-5 space-y-1">
                {t("business").split(",").map((item, index) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">{t("partnerTitle")}</TableCell>
            <TableCell>
              <p>{t("partner").split(",").map((item, index) => (
                <li key={index}>{item}</li>
              ))}</p>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">{t("allianceTitle")}</TableCell>
            <TableCell>
              <ul className="list-disc pl-5 space-y-1">
                {t("alliance").split(",").map((item, index) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">{t("banksTitle")}</TableCell>
            <TableCell>
              <p>{t("banks")}</p>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
}
