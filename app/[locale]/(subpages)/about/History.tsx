"use client";

import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";

export default function CompanyHistory() {
  const t = useTranslations("About");
  return (
    <div className="bg-gray-100 w-full">
      <div className="max-w-4xl mx-auto my-4 p-4">

        <h2 className="text-xl sm:text-2xl font-bold text-center w-full">
          {t("companyHistory")}
        </h2>

        <Separator className="w-full my-4" />

        <div className="relative">
          {/* Vertical Line */}
          <div className="absolute left-[181px] top-3 bottom-3 w-0.5 bg-neutral-200"></div>

          <ul className="space-y-6">
            {[
              { date: t("event1Title"), event: t("event1") },
              { date: t("event2Title"), event: t("event2") },
              { date: t("event3Title"), event: t("event3") },
              { date: t("event4Title"), event: t("event4") },
              { date: t("event5Title"), event: t("event5") },
              { date: t("event6Title"), event: t("event6") },
              { date: t("event7Title"), event: t("event7") },
              { date: t("event8Title"), event: t("event8") },
            ].map((item, index) => (
              <li key={index} className="flex items-center space-x-4 justify-between w-full">
                {/* Date */}
                <span className="text-gray-500 font-medium w-40 text-right">{item.date}</span>

                {/* Circle Indicator */}
                <span className="relative z-10 w-3 h-3 bg-white border border-blue-400 rounded-full"></span>

                {/* Event Text */}
                <p className="text-gray-700 flex-1">{item.event}</p>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}
