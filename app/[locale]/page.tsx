"use client"

import { useEffect, useRef, useState } from "react";
import Typed from "typed.js";
import Image from "next/image";
import { useTranslations } from "next-intl";
import Strength from "./(components)/Strength";
import Services from "./(components)/Services";
import About from "./(components)/About";
import Team from "./(components)/Team";
import Inquiry from "./(components)/Inquiry";
import { motion, useAnimation } from "motion/react";

export default function BackgroundVideo() {
  const t = useTranslations("Homepage");
  const typedRef = useRef<HTMLSpanElement>(null);
  const controls = useAnimation();
  const [hideScroll, setHideScroll] = useState(false);

  // Function to scroll down
  const scrollToContent = () => {
    window.scrollTo({
      top: window.innerHeight,
      behavior: "smooth",
    });
  };

  useEffect(() => {
    const options = {
      strings: [
        t("typed.options1"),
        t("typed.options2"),
        t("typed.options3"),
        t("typed.options4"),
        t("typed.options5")
      ],
      typeSpeed: 80,
      backSpeed: 20,
      loop: true,
    };

    const typed = new Typed(typedRef.current!, options);

    return () => {
      typed.destroy();
    };
  }, [t]);

  const handleScroll = () => {
    const scrollY = window.scrollY;
    controls.start({
      scale: scrollY > 0 ? 1.1 : 1,
      transition: { duration: 0.5 }
    });
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [controls]);

  return (
    <>
      <div
        id="background-video-wrapper"
        style={{
          position: 'relative',
          width: '100vw',
          height: '100vh',
        }}
      >
        {/* 在移动设备上显示图像 */}
        <video
          className="absolute top-0 left-0 w-full h-full object-cover hidden md:block"
          autoPlay
          loop
          muted
        // controls
        >
          <source src="/videos/video_compressed.mp4" type="video/mp4" />
        </video>
        <Image
          src="/assets/bg1.avif"
          className="absolute top-0 left-0 w-full h-full object-cover md:hidden"
          alt="Background"
          width={1920}
          height={1080}
        />
        <div
          className="absolute top-0 left-0 w-full h-full flex items-center justify-center bg-black bg-opacity-50 text-white"
        >
          <div className="absolute z-20 flex flex-col items-center justify-center w-full h-full text-white font-bold text-center">
            <div className="text-4xl md:text-5xl lg:text-6xl tracking-wide mb-1 md:mb-4 leading-relaxed">
              {t("title")}
            </div>

            <div className="text-2xl md:text-3xl lg:text-4xl tracking-wide font-bold mb-5 leading-relaxed">
              {t("adj")} <span ref={typedRef}></span>
            </div>
          </div>
        </div>

        {/* Scroll Down Indicator */}
        {!hideScroll && (
          <div
            className="absolute bottom-4 sm:bottom-8 left-[calc(50%-30px)] transform -translate-x-1/2 flex flex-col items-center cursor-pointer animate-bounce z-20 opacity-80"
            onClick={scrollToContent}
          >
            <span className="text-white text-sm">{t("scrollDown")}</span>
            <svg
              className="w-6 h-6 text-white mt-1"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        )}

        <style jsx>{`
        #background-video-wrapper::after {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 10;
          width: 100%;
          height: 100%;
          background-image: url('/videos/pattern.png');
          background-repeat: repeat;
          opacity: 1;
          content: '';
          pointer-events: none;
        }
      `}</style>
      </div>

      <motion.div
        initial={{ y: '-5%', opacity: 0.1 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 1 }}
      >
        <Services />
      </motion.div>

      <motion.div
        initial={{ y: '-5%', opacity: 0.1 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 1 }}
      >
        <Strength />
      </motion.div>

      <motion.div
        initial={{ y: '-5%', opacity: 0.1 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 1 }}
      >
        <About />
      </motion.div>

      <motion.div
        initial={{ y: '-5%', opacity: 0.1 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 1 }}
      >
        <Team />
      </motion.div>

      <motion.div
        initial={{ y: '-5%', opacity: 0.1 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 1 }}
      >
        <Inquiry />
      </motion.div>
    </>
  );
}
